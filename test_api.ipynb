#%%
import requests
import base64
from PIL import Image
import io

# Create a simple test image
def create_test_image():
    img = Image.new('RGB', (256, 256), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode()

# Test the synchronous /generate endpoint
API_URL = "http://localhost:8081"

# Minimal request with only required parameter
request_data = {
    "image": create_test_image()
}

print("Testing /generate endpoint...")
try:
    response = requests.post(f"{API_URL}/generate", json=request_data)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        # Save the generated GLB file
        with open("test_output.glb", "wb") as f:
            f.write(response.content)
        print("Success! GLB file saved as 'test_output.glb'")
    else:
        print(f"Error: {response.text}")
        
except requests.exceptions.ConnectionError:
    print("Error: Could not connect to API server at localhost:8081")
#%%


#%% md
